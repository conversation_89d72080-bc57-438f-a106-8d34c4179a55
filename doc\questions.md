# Questions fréquentes nnU-Net

---

## 1. Comment fonctionne l'option `FOLD` ?

- L'option `FOLD` dans nnU-Net fonctionne avec la validation croisée à 5 plis :
  - **FOLD 0, 1, 2, 3, 4** :
    - Chaque fold utilise **4/5 des données pour l'entraînement** et **1/5 pour la validation**.
  - **FOLD "all"** :
    - Utilise **TOUTES** les images à la fois pour l'entraînement **ET** la validation (pas de séparation).
- Le système lit le fichier `splits_final.json` qui contient les divisions prédéfinies.
- Si ce fichier n'existe pas, nnU-Net crée automatiquement une division 5-fold avec une graine fixe (`seed=12345`).

---

## 2. Comment utiliser un dossier de validation spécifique ?

- nnU-Net **ne supporte pas nativement** l'utilisation d'un dataset de validation complètement différent.
- Le système est conçu pour utiliser les splits définis dans `splits_final.json` du même dataset.

**<PERSON>ur contourner cela, vous pouvez :**

1. Créer manuellement un fichier `splits_final.json` personnalisé
2. Utiliser `FOLD "all"` et gérer la validation séparément
3. Modifier le code du trainer pour pointer vers un autre dataset

---

## 3. Est-ce que l'orientation des images est importante ?

- **OUI, l'orientation est CRITIQUE.**
- nnU-Net est entraîné sur un ordre d'axes spécifique qui **ne peut pas être perturbé lors de l'inférence**.
- Si vous entraînez sur `500x250` et faites l'inférence sur `250x500`, il y aura des problèmes.
- nnU-Net s'attend à ce que les images d'inférence aient **la même orientation que celles d'entraînement**.

> **La solution :**
> Utiliser la **même classe I/O** que celle utilisée pendant le preprocessing (définie dans `plans.json` sous la clé `"image_reader_writer"`).